#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# === CONFIGURATION (MODIFIEZ ICI) ===
INFERENCE_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\imagesTestInference"

def main():
    """Renomme les fichiers d'un dossier au format nnU-Net pour l'inférence"""

    inference_dir = Path(INFERENCE_FOLDER)

    # Vérification du dossier
    if not inference_dir.exists():
        sys.exit(f"❌ Dossier introuvable: {inference_dir}")

    # Collecter les fichiers images
    images = sorted([f for f in os.listdir(inference_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])

    if not images:
        sys.exit(f"❌ Aucun fichier image trouvé dans {inference_dir}")

    print(f"🔄 Renommage de {len(images)} fichiers au format nnU-Net...")

    # Renommer les fichiers
    for idx, img in enumerate(images, 1):
        old_path = inference_dir / img
        new_name = f"{idx:04d}_0000.png"
        new_path = inference_dir / new_name

        # Renommer le fichier
        os.rename(old_path, new_path)
        print(f"   {img} -> {new_name}")

    print("✅ Renommage terminé avec succès.")
    print(f"📁 Dossier prêt pour l'inférence: {inference_dir}")

if __name__ == "__main__":
    main()
